{% extends "base.html" %}

{% block title %}لوحة التحكم - CMSVS{% endblock %}

{% block content %}
<style>
/* Table hover effects management */
.table tbody tr:hover {
    background-color: #f9fafb;
}

/* Disable hover effects when dropdown is open */
.table.dropdown-open tbody tr:hover {
    background-color: transparent !important;
    transform: none !important;
    box-shadow: none !important;
}

.table.dropdown-open tbody td:hover {
    background-color: transparent !important;
    transform: none !important;
    box-shadow: none !important;
    z-index: 1 !important;
}

.table.dropdown-open tbody tr {
    z-index: 1 !important;
}

.table.dropdown-open tbody td {
    z-index: 1 !important;
}

/* Ensure dropdown is completely opaque and visible */
.dropdown-menu {
    opacity: 1 !important;
    visibility: visible !important;
}

.dropdown-menu.hidden {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Force dropdown to be visible when shown */
.dropdown-menu:not(.hidden) {
    display: block !important;
    z-index: 999999 !important;
    position: fixed !important;
    background-color: #ffffff !important;
    background: #ffffff !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fix any ring opacity conflicts */
.dropdown-menu * {
    opacity: 1 !important;
}

/* Ensure dropdown items are fully visible */
.dropdown-menu a,
.dropdown-menu button,
.dropdown-menu div {
    opacity: 1 !important;
}
</style>

<div class="space-y-6">
    <!-- Welcome Header -->
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-3xl font-bold text-gray-900">
                مرحباً، {{ current_user.full_name }}
            </h2>
            <p class="text-gray-600 mt-1">إليك نظرة عامة على نشاطك</p>
        </div>
        <a href="/requests/new" class="btn-primary">
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            طلب جديد
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-2xl font-bold text-gray-900">{{ stats.total }}</h3>
                        <p class="text-sm text-gray-600">إجمالي الطلبات</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-warning-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-2xl font-bold text-gray-900">{{ stats.pending }}</h3>
                        <p class="text-sm text-gray-600">طلبات قيد المراجعة</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-2xl font-bold text-gray-900">{{ stats.completed }}</h3>
                        <p class="text-sm text-gray-600">طلبات مكتملة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Requests -->
        <div class="card">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <h5 class="text-lg font-semibold text-gray-900">الطلبات الأخيرة</h5>
                    <div class="flex space-x-2 rtl:space-x-reverse">
                        <a href="/requests/new" class="text-sm text-primary-600 hover:text-primary-500">
                            طلب جديد
                        </a>
                        <span class="text-gray-300">|</span>
                        <a href="/requests" class="text-sm text-primary-600 hover:text-primary-500">
                            عرض الكل
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                {% if requests %}
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead class="table-header">
                            <tr>
                                <th class="table-header-cell">رقم الطلب</th>
                                <th class="table-header-cell">العنوان</th>
                                <th class="table-header-cell">الحالة</th>
                                <th class="table-header-cell">التاريخ</th>
                                <th class="table-header-cell">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="table-body" id="requests-tbody">
                            {% for req in requests %}
                            <tr>
                                <td class="table-cell">
                                    <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ req.request_number }}</code>
                                </td>
                                <td class="table-cell">
                                    <div class="max-w-xs truncate">{{ req.request_title }}</div>
                                </td>
                                <td class="table-cell">
                                    {% if req.status.value == 'pending' %}
                                    <span class="badge-warning">قيد المراجعة</span>
                                    {% elif req.status.value == 'in_progress' %}
                                    <span class="badge-info">قيد التنفيذ</span>
                                    {% elif req.status.value == 'completed' %}
                                    <span class="badge-success">مكتمل</span>
                                    {% elif req.status.value == 'rejected' %}
                                    <span class="badge-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td class="table-cell text-sm text-gray-500">
                                    {{ req.created_at.strftime('%Y-%m-%d') }}
                                </td>
                                <td class="table-cell">
                                    <div class="flex space-x-2 rtl:space-x-reverse">
                                        <a href="/requests/{{ req.id }}" class="text-primary-600 hover:text-primary-500 text-sm">
                                            عرض
                                        </a>
                                        <div class="relative inline-block text-left">
                                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="toggleDropdown({{ req.id }}, event)">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                                </svg>
                                            </button>
                                            <div id="dropdown-{{ req.id }}" class="hidden w-48 rounded-md shadow-lg bg-white border border-gray-200">
                                                <div class="py-1">
                                                    <div class="px-4 py-2 text-xs text-gray-500 border-b">إجراءات الطلب</div>

                                                    <!-- Edit Request Option -->
                                                    <a href="/requests/{{ req.id }}/edit" class="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                                        <i class="fas fa-edit text-blue-600 ml-2"></i>
                                                        تعديل الطلب
                                                    </a>

                                                    <!-- File Management Option -->
                                                    <a href="/requests/{{ req.id }}/files" class="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                                        <i class="fas fa-file-upload text-green-600 ml-2"></i>
                                                        إدارة الملفات
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}

                            <!-- Load More Button Row -->
                            {% if requests|length == 10 %}
                            <tr id="load-more-row">
                                <td colspan="5" class="table-cell text-center">
                                    <button hx-get="/api/requests/load-more?skip=10"
                                            hx-target="#requests-tbody"
                                            hx-swap="beforeend"
                                            hx-indicator="#loading-indicator"
                                            onclick="this.closest('tr').remove()"
                                            class="btn-secondary">
                                        تحميل المزيد
                                    </button>
                                    <div id="loading-indicator" class="htmx-indicator">
                                        <div class="flex items-center justify-center">
                                            <svg class="animate-spin h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            <span class="mr-2">جاري التحميل...</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-600">لا توجد طلبات حتى الآن</p>
                    <a href="/requests/new" class="mt-4 btn-primary">
                        إنشاء طلب جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="card">
            <div class="card-header">
                <h5 class="text-lg font-semibold text-gray-900">النشاطات الأخيرة</h5>
            </div>
            <div class="card-body">
                {% if activities %}
                <div class="space-y-4">
                    {% for activity in activities %}
                    <div class="flex items-start space-x-3 rtl:space-x-reverse">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                {% if activity.activity_type.value == 'login' %}
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                </svg>
                                {% elif activity.activity_type.value == 'request_created' %}
                                <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                {% elif activity.activity_type.value == 'file_uploaded' %}
                                <svg class="w-4 h-4 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                {% else %}
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {% endif %}
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-900">{{ activity.description }}</p>
                            <small class="text-xs text-gray-500">{{ activity.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <p class="text-sm text-gray-600">لا توجد نشاطات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function toggleDropdown(requestId, event) {
    // Prevent default behavior and stop propagation
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const dropdown = document.getElementById(`dropdown-${requestId}`);
    const isHidden = dropdown.classList.contains('hidden');

    // Close all other dropdowns first
    document.querySelectorAll('[id^="dropdown-"]').forEach(d => {
        if (d.id !== `dropdown-${requestId}`) {
            d.classList.add('hidden');
        }
    });

    if (isHidden) {
        // Position dropdown intelligently
        positionDropdown(dropdown, event.target);
        dropdown.classList.remove('hidden');

        // Add class to table to disable hover effects
        const table = dropdown.closest('table');
        if (table) {
            table.classList.add('dropdown-open');
        }
    } else {
        dropdown.classList.add('hidden');

        // Remove class from table to re-enable hover effects
        const table = dropdown.closest('table');
        if (table) {
            table.classList.remove('dropdown-open');
        }
    }

    // Prevent page scrolling
    return false;
}

function positionDropdown(dropdown, button) {
    // Reset classes first
    dropdown.classList.remove('left-0', 'right-0', 'origin-top-left', 'origin-top-right', 'origin-bottom-left', 'origin-bottom-right');
    dropdown.classList.remove('mt-2', 'mb-2');
    dropdown.style.width = '';

    // Ensure proper styling
    dropdown.style.position = 'fixed';
    dropdown.style.zIndex = '999999';
    dropdown.style.backgroundColor = '#ffffff';
    dropdown.style.background = '#ffffff';
    dropdown.style.opacity = '1';
    dropdown.style.visibility = 'visible';

    // Get button and viewport dimensions
    const buttonRect = button.getBoundingClientRect();
    const dropdownWidth = 192; // w-48 = 12rem = 192px (smaller for user dashboard)
    const dropdownHeight = 120; // Estimated dropdown height (smaller for user dashboard)
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Calculate horizontal position
    let leftPosition;
    const spaceOnRight = viewportWidth - buttonRect.right;
    if (spaceOnRight >= dropdownWidth) {
        // Enough space on the right
        leftPosition = buttonRect.left;
    } else {
        // Not enough space on right, align to right edge of button
        leftPosition = buttonRect.right - dropdownWidth;
    }
    // Ensure it doesn't go off-screen
    leftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - dropdownWidth - 10));

    dropdown.style.left = leftPosition + 'px';
    dropdown.style.width = dropdownWidth + 'px';

    // Calculate vertical position
    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;

    if (spaceBelow >= dropdownHeight || spaceBelow > spaceAbove) {
        // Position below button
        dropdown.style.top = (buttonRect.bottom + 5) + 'px';
        dropdown.style.bottom = 'auto';
    } else {
        // Position above button
        dropdown.style.bottom = (viewportHeight - buttonRect.top + 5) + 'px';
        dropdown.style.top = 'auto';
    }

    // Ensure dropdown is visible
    dropdown.style.visibility = 'visible';
    dropdown.style.opacity = '1';
}

// Dropdown management with proper cleanup
(function() {
    let isInitialized = false;

    function closeAllDropdowns() {
        document.querySelectorAll('[id^="dropdown-"]').forEach(dropdown => {
            dropdown.classList.add('hidden');

            // Remove dropdown-open class from table
            const table = dropdown.closest('table');
            if (table) {
                table.classList.remove('dropdown-open');
            }
        });
    }

    function handleOutsideClick(event) {
        const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
        const clickedButton = event.target.closest('button[onclick*="toggleDropdown"]');

        dropdowns.forEach(dropdown => {
            // Don't close if clicking on the dropdown itself or its toggle button
            if (!dropdown.contains(event.target) && !clickedButton) {
                dropdown.classList.add('hidden');

                // Remove dropdown-open class from table
                const table = dropdown.closest('table');
                if (table) {
                    table.classList.remove('dropdown-open');
                }
            }
        });
    }

    function handleKeydown(event) {
        const target = event.target;
        if (target.tagName === 'BUTTON' && target.onclick && target.onclick.toString().includes('toggleDropdown')) {
            if (event.key === ' ' || event.key === 'Enter') {
                event.preventDefault();
                target.click();
            }
        }
    }

    function initializeDropdownListeners() {
        if (isInitialized) return;

        // Add event listeners
        document.addEventListener('click', handleOutsideClick);
        document.addEventListener('keydown', handleKeydown);

        isInitialized = true;
    }

    function cleanupDropdownListeners() {
        if (!isInitialized) return;

        // Remove event listeners
        document.removeEventListener('click', handleOutsideClick);
        document.removeEventListener('keydown', handleKeydown);

        isInitialized = false;
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeDropdownListeners);
    } else {
        initializeDropdownListeners();
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', cleanupDropdownListeners);

    // Expose cleanup function globally for manual cleanup if needed
    window.cleanupUserDashboardDropdowns = cleanupDropdownListeners;
})();
</script>
{% endblock %}